<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Change Required - Computer Shop</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .password-change-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .password-change-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .password-change-header i {
            font-size: 3rem;
            color: #ffc107;
            margin-bottom: 1rem;
        }

        .password-change-header h1 {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .password-change-header p {
            color: #666;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .password-requirements {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #17a2b8;
        }

        .password-requirements h4 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .password-requirements ul {
            list-style: none;
            padding-left: 0;
        }

        .password-requirements li {
            color: #666;
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        .password-requirements li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }

        .btn-change-password {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-change-password:hover {
            transform: translateY(-2px);
        }

        .alert {
            padding: 0.75rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .user-info {
            text-align: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #e9ecef;
            border-radius: 5px;
        }

        .user-info strong {
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="password-change-container">
        <div class="password-change-header">
            <i class="fas fa-key"></i>
            <h1>Password Change Required</h1>
            <p>You must change your password to continue</p>
        </div>

        {% if username %}
        <div class="user-info">
            <strong>User: {{ username }}</strong>
        </div>
        {% endif %}

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="post">
            <div class="form-group">
                <label for="new_password">New Password:</label>
                <input type="password" id="new_password" name="new_password" required>
            </div>

            <div class="form-group">
                <label for="confirm_password">Confirm New Password:</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>

            <div class="password-requirements">
                <h4>Password Requirements:</h4>
                <ul>
                    <li>At least 8 characters long</li>
                    <li>Must be different from your current password</li>
                    <li>Should contain a mix of letters and numbers</li>
                </ul>
            </div>

            <button type="submit" class="btn-change-password">
                <i class="fas fa-lock"></i> Change Password
            </button>
        </form>
    </div>

    <script>
        // Add real-time password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirm = this.value;
            
            if (confirm && password !== confirm) {
                this.style.borderColor = '#dc3545';
            } else {
                this.style.borderColor = '#ddd';
            }
        });

        // Add password strength indicator
        document.getElementById('new_password').addEventListener('input', function() {
            const password = this.value;
            
            if (password.length >= 8) {
                this.style.borderColor = '#28a745';
            } else if (password.length >= 4) {
                this.style.borderColor = '#ffc107';
            } else {
                this.style.borderColor = '#dc3545';
            }
        });
    </script>
</body>
</html>
