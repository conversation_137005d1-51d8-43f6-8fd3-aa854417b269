{% extends "base.html" %}

{% block title %}Customer Management{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="/static/css/staff_customers.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
{% endblock %}

{% block content %}
<div class="staff-container">
    <h1>Customer Management</h1>
    {% if session.get('role') != 'staff' %}
    <button type="button" class="btn btn-success mb-3" id="addCustomerBtn">Add New Customer</button>
    {% endif %}

    <!-- Search Input -->
    <div class="mb-3">
        <input type="text" id="customerSearchInput" class="form-control" placeholder="Search customers by name or email" aria-label="Search customers">
    </div>

    <!-- Customer Table -->
    <div class="customer-table-container" id="customers-container">
        <table id="customersTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for customer in customers %}
                <tr>
                    <td>{{ customer.id }}</td>
                    <td>{{ customer.first_name }} {{ customer.last_name }}</td>
                    <td>{{ customer.email }}</td>
                    <td>{{ customer.phone }}</td>
                    <td>
                        <div class="action-buttons">
                            <button type="button" class="btn btn-sm btn-info view-orders-btn" data-customer-id="{{ customer.id }}">Orders</button>
                            {% if session.get('role') != 'staff' %}
                            <button type="button" class="btn btn-sm btn-primary edit-customer-btn" data-customer-id="{{ customer.id }}">Edit</button>
                            <button type="button" class="btn btn-sm btn-danger delete-customer-btn" data-customer-id="{{ customer.id }}">Delete</button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="5" class="text-center">No customers found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        <div id="mobile-customers-list"></div>

        <!-- Pagination -->
        <nav aria-label="Customer pagination" class="mt-3">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- Pagination buttons will be dynamically added here -->
            </ul>
        </nav>
    </div>

    <!-- Modal for Completed Orders Count -->
    <div class="modal fade" id="completedOrdersModal" tabindex="-1" aria-labelledby="completedOrdersModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="completedOrdersModalLabel">Completed Orders Count</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" id="completedOrdersCountBody">
            Loading...
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>

   <!-- Customer Orders Modal -->
   <div class="modal fade" id="customerOrdersModal" tabindex="-1" aria-labelledby="customerOrdersModalLabel" aria-hidden="true">
       <div class="modal-dialog modal-lg">
           <div class="modal-content">
               <div class="modal-header">
                   <h5 class="modal-title" id="customerOrdersModalLabel">Customer Orders Detail</h5>
                   <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
               </div>
               <div class="modal-body" id="customerOrdersModalBody">
                   Loading...
               </div>
               <div class="modal-footer">
                   <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
               </div>
           </div>
       </div>
   </div>

   <!-- Add/Edit Customer Modal -->
   <div class="modal fade" id="customerModal" tabindex="-1" aria-hidden="true">
       <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Add Customer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="customerForm">
                        <input type="hidden" id="customerId">
                        <div class="mb-3">
                            <label for="firstName" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="firstName" required>
                        </div>
                        <div class="mb-3">
                            <label for="lastName" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="lastName" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="phone">
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="saveCustomerBtn">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Message Container for Notifications -->
<div id="message-container"></div>

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
<script src="/static/js/item-counter.js"></script>
<script src="/static/js/staff_messages.js"></script>
<script src="/static/js/staff_customers.js"></script>
{% endblock %}
