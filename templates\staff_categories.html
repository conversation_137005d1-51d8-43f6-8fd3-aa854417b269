{% extends "base.html" %}

{% block title %}Category Management{% endblock %}

{% block content %}
<div class="staff-container">
    <h1>Category Management</h1>

    <!-- Add Category Form -->
    <div class="inventory-actions">
        <div class="inventory-search">
            <form method="post" class="form-container">
                <div class="form-group flex-1">
                    <label for="name" class="form-label">Category Name:</label>
                    <input type="text" id="name" name="name" required class="form-input">
                </div>
                <div class="form-group flex-2">
                    <label for="description" class="form-label">Description (Optional):</label>
                    <input type="text" id="description" name="description" class="form-input">
                </div>
                <div class="form-group">
                    <div class="form-label-spacer"></div>
                    <button type="submit" class="form-submit">Add Category</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="inventory-list">
        <table class="table-container">
            <thead>
                <tr>
                    <th class="table-header">ID</th>
                    <th class="table-header">Category Name</th>
                    <th class="table-header">Description</th>
                    <th class="table-header center">Products Count</th>
                    <th class="table-header center">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for category in categories %}
                <tr>
                    <td class="table-cell">{{ category.id }}</td>
                    <td class="table-cell">
                        <span class="name-display">{{ category.name }}</span>
                        <input type="text" class="name-edit edit-input" value="{{ category.name }}">
                    </td>
                    <td class="table-cell">
                        <span class="description-display">{{ category.description or 'No description' }}</span>
                        <input type="text" class="description-edit edit-input" value="{{ category.description or '' }}">
                    </td>
                    <td class="table-cell center">
                        <span id="product-count-{{ category.id }}">Loading...</span>
                    </td>
                    <td class="table-cell center">
                        <div class="action-buttons">
                            <button class="edit-btn" onclick="editCategory('{{ category.id }}')">Edit</button>
                            <button class="save-btn" onclick="saveCategory('{{ category.id }}')">Save</button>
                            <button class="cancel-btn" onclick="cancelEdit('{{ category.id }}')">Cancel</button>
                            <button class="delete-btn" data-category-name="{{ category.name }}" onclick="deleteCategory('{{ category.id }}', this.getAttribute('data-category-name'))">Delete</button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Message Container for Notifications -->
<div id="message-container"></div>

{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
<style>
    .staff-container {
        width: 100%;
        margin: 0 auto;
        padding: 20px;
    }

    .inventory-actions {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: 20px 0;
        gap: 15px;
    }

    .inventory-search {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        max-width: 1000px;
    }

    .inventory-list table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #aaa;
        font-weight: 400;
    }

    .inventory-list th,
    .inventory-list td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #888;
    }

    .inventory-list th {
        background-color: #f5f5f5;
        font-weight: bold;
    }

    .inventory-list tbody tr:hover {
        background-color: #f8f9fa;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
        align-items: center;
    }

    .action-buttons button {
        transition: background-color 0.2s;
    }

    .action-buttons button:hover {
        opacity: 0.8;
    }

    .edit-btn {
        background-color: #ffc107;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        margin-right: 5px;
    }

    .save-btn {
        display: none;
        background-color: #28a745;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        margin-right: 5px;
    }

    .cancel-btn {
        display: none;
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        margin-right: 5px;
    }

    .delete-btn {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
    }

    .form-container {
        display: flex;
        gap: 15px;
        align-items: flex-end;
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-group.flex-1 {
        flex: 1;
        min-width: 200px;
    }

    .form-group.flex-2 {
        flex: 1.5;
        min-width: 250px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        height: 20px;
        line-height: 20px;
    }

    .form-input {
        padding: 10px 12px;
        border: 1px solid #ccc;
        border-radius: 5px;
        width: 100%;
        min-width: 200px;
        height: 40px;
        box-sizing: border-box;
    }

    .form-submit {
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        min-width: 130px;
        height: 40px;
        box-sizing: border-box;
    }

    .form-label-spacer {
        height: 20px;
        margin-bottom: 8px;
    }

    .edit-input {
        display: none;
        padding: 4px;
        border: 1px solid #ccc;
        border-radius: 3px;
        width: 100%;
        min-width: 150px;
        box-sizing: border-box;
    }

    .table-container {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #aaa;
        font-weight: 400;
    }

    .table-cell {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #888;
    }

    .table-cell.center {
        text-align: center;
    }

    .table-header {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #888;
        background-color: #f5f5f5;
    }

    .table-header.center {
        text-align: center;
    }
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/staff_messages.js') }}"></script>
<script>
// Load product counts for each category
document.addEventListener('DOMContentLoaded', function() {
    loadProductCounts();
});

function loadProductCounts() {
    const categoryRows = document.querySelectorAll('tbody tr');
    categoryRows.forEach(row => {
        try {
            const editButton = row.querySelector('button.edit-btn');
            if (!editButton) return;
            
            const onclickAttr = editButton.getAttribute('onclick');
            if (!onclickAttr) return;
            
            const match = onclickAttr.match(/editCategory\('(\d+)'\)/);
            if (!match) return;
            
            const categoryId = match[1];
            const countElement = document.getElementById(`product-count-${categoryId}`);
            if (!countElement) return;

            fetch(`/api/staff/categories/${categoryId}/products/count`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    countElement.textContent = data.count;
                } else {
                    countElement.textContent = '0';
                }
            })
            .catch(error => {
                console.error('Error loading product count:', error);
                countElement.textContent = 'Error';
            });
        } catch (error) {
            console.error('Error processing category row:', error);
        }
    });
}

function editCategory(categoryId) {
    // First try to find the row using the product count ID as it's unique
    const countSpan = document.getElementById(`product-count-${categoryId}`);
    if (!countSpan) {
        console.error('Could not find category row');
        return;
    }
    
    const row = countSpan.closest('tr');
    if (!row) {
        console.error('Could not find table row');
        return;
    }

    // Hide display elements and show edit elements
    row.querySelector('.name-display').style.display = 'none';
    row.querySelector('.name-edit').style.display = 'inline';
    row.querySelector('.description-display').style.display = 'none';
    row.querySelector('.description-edit').style.display = 'inline';

    // Hide edit button, show save/cancel buttons
    row.querySelector('.edit-btn').style.display = 'none';
    row.querySelector('.save-btn').style.display = 'inline';
    row.querySelector('.cancel-btn').style.display = 'inline';
}

function cancelEdit(categoryId) {
    const countSpan = document.getElementById(`product-count-${categoryId}`);
    if (!countSpan) {
        console.error('Could not find category row');
        return;
    }
    
    const row = countSpan.closest('tr');
    if (!row) {
        console.error('Could not find table row');
        return;
    }

    // Show display elements and hide edit elements
    row.querySelector('.name-display').style.display = 'inline';
    row.querySelector('.name-edit').style.display = 'none';
    row.querySelector('.description-display').style.display = 'inline';
    row.querySelector('.description-edit').style.display = 'none';

    // Show edit button, hide save/cancel buttons
    row.querySelector('.edit-btn').style.display = 'inline';
    row.querySelector('.save-btn').style.display = 'none';
    row.querySelector('.cancel-btn').style.display = 'none';

    // Reset values
    const originalName = row.querySelector('.name-display').textContent;
    const originalDescription = row.querySelector('.description-display').textContent;
    row.querySelector('.name-edit').value = originalName;
    row.querySelector('.description-edit').value = originalDescription === 'No description' ? '' : originalDescription;
}

function saveCategory(categoryId) {
    const countSpan = document.getElementById(`product-count-${categoryId}`);
    if (!countSpan) {
        console.error('Could not find category row');
        return;
    }
    
    const row = countSpan.closest('tr');
    if (!row) {
        console.error('Could not find table row');
        return;
    }
    
    const name = row.querySelector('.name-edit').value.trim();
    const description = row.querySelector('.description-edit').value.trim();

    if (!name) {
        showMessage('Category name cannot be empty', 'error');
        return;
    }

    fetch(`/auth/api/staff/categories/${categoryId}/update`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: name,
            description: description
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Update display values
            row.querySelector('.name-display').textContent = name;
            row.querySelector('.description-display').textContent = description || 'No description';

            cancelEdit(categoryId);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error updating category', 'error');
    });
}

function deleteCategory(categoryId, categoryName) {
    if (confirm(`Are you sure you want to delete category "${categoryName}"? This action cannot be undone.`)) {
        fetch(`/auth/api/staff/categories/${categoryId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                try {
                    // Try to find the row by a more specific selector
                    const row = document.querySelector(`tr:has(span#product-count-${categoryId})`);
                    if (row) {
                        row.remove();
                    } else {
                        // Fallback: try to find any element containing the category ID and traverse up to tr
                        const anyElement = document.getElementById(`product-count-${categoryId}`);
                        if (anyElement) {
                            const row = anyElement.closest('tr');
                            if (row) {
                                row.remove();
                            }
                        }
                    }
                } catch (removeError) {
                    console.error('Error removing row:', removeError);
                    // The category was deleted from database but UI removal failed
                    // Refresh the page as fallback
                    window.location.reload();
                }
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error deleting category', 'error');
        });
    }
}
</script>
{% endblock %}
